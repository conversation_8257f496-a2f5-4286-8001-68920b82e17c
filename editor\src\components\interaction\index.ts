/**
 * 交互组件导出
 */

// 主要组件
export { default as InteractionEditor } from './InteractionEditor';
export { default as InteractionPreview } from './InteractionPreview';
export { default as InteractionToolbar } from './InteractionToolbar';
export { default as InteractionManager } from './InteractionManager';

// 类型定义
export {
  InteractionType,
  InteractionEventType,
  HighlightType,
  PromptPositionType
} from './InteractionEditor';

// 服务
export { InteractionService, interactionService } from '../../services/InteractionService';
export type {
  InteractionEvent,
  InteractionStats,
  InteractionServiceConfig
} from '../../services/InteractionService';

// 工具函数
export const createDefaultInteractionConfig = () => ({
  interactionType: InteractionType.CLICK,
  visible: true,
  interactive: true,
  interactionDistance: 5.0,
  label: '可交互对象',
  prompt: '按E键交互',
  highlightColor: '#ffff00',
  highlightType: HighlightType.OUTLINE,
  enableHighlight: true,
  enablePrompt: true,
  enableSound: false,
  priority: 0,
  conditions: [],
  events: []
});

export const validateInteractionConfig = (config: any): boolean => {
  if (!config) return false;
  
  // 基础验证
  if (typeof config.interactionType !== 'string') return false;
  if (typeof config.visible !== 'boolean') return false;
  if (typeof config.interactive !== 'boolean') return false;
  if (typeof config.interactionDistance !== 'number' || config.interactionDistance <= 0) return false;
  
  // 可选字段验证
  if (config.label && typeof config.label !== 'string') return false;
  if (config.prompt && typeof config.prompt !== 'string') return false;
  if (config.highlightColor && typeof config.highlightColor !== 'string') return false;
  if (config.priority && typeof config.priority !== 'number') return false;
  
  // 数组字段验证
  if (config.conditions && !Array.isArray(config.conditions)) return false;
  if (config.events && !Array.isArray(config.events)) return false;
  
  return true;
};

export const getInteractionTypeDisplayName = (type: InteractionType, t: any): string => {
  const typeMap = {
    [InteractionType.CLICK]: t('interaction.click'),
    [InteractionType.HOVER]: t('interaction.hover'),
    [InteractionType.PROXIMITY]: t('interaction.proximity'),
    [InteractionType.GRAB]: t('interaction.grab'),
    [InteractionType.TOUCH]: t('interaction.touch')
  };
  
  return typeMap[type] || type;
};

export const getHighlightTypeDisplayName = (type: HighlightType, t: any): string => {
  const typeMap = {
    [HighlightType.OUTLINE]: t('interaction.outline'),
    [HighlightType.GLOW]: t('interaction.glow'),
    [HighlightType.COLOR]: t('interaction.color'),
    [HighlightType.SCALE]: t('interaction.scale')
  };
  
  return typeMap[type] || type;
};

// 常量
export const INTERACTION_CONSTANTS = {
  DEFAULT_DISTANCE: 5.0,
  MIN_DISTANCE: 0.1,
  MAX_DISTANCE: 100.0,
  DEFAULT_PRIORITY: 0,
  MIN_PRIORITY: -10,
  MAX_PRIORITY: 10,
  DEFAULT_HIGHLIGHT_COLOR: '#ffff00',
  MAX_RECENT_EVENTS: 100,
  STATS_UPDATE_INTERVAL: 2000
};

// 预设配置
export const INTERACTION_PRESETS = {
  BUTTON: {
    interactionType: InteractionType.CLICK,
    interactionDistance: 3.0,
    label: '按钮',
    prompt: '点击',
    enableHighlight: true,
    highlightType: HighlightType.OUTLINE,
    highlightColor: '#1890ff'
  },
  DOOR: {
    interactionType: InteractionType.PROXIMITY,
    interactionDistance: 2.0,
    label: '门',
    prompt: '靠近开门',
    enableHighlight: true,
    highlightType: HighlightType.GLOW,
    highlightColor: '#52c41a'
  },
  PICKUP: {
    interactionType: InteractionType.GRAB,
    interactionDistance: 1.5,
    label: '物品',
    prompt: '拾取',
    enableHighlight: true,
    highlightType: HighlightType.COLOR,
    highlightColor: '#faad14'
  },
  INFO_POINT: {
    interactionType: InteractionType.HOVER,
    interactionDistance: 4.0,
    label: '信息点',
    prompt: '查看信息',
    enableHighlight: true,
    highlightType: HighlightType.SCALE,
    highlightColor: '#722ed1'
  }
};
