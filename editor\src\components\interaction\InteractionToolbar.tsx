/**
 * 交互工具栏组件
 * 提供快速创建和编辑交互的工具
 */
import React, { useState, useCallback } from 'react';
import {
  Toolbar,
  Button,
  Dropdown,
  Space,
  Tooltip,
  Badge,
  Popover,
  Switch,
  Slider,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  SettingOutlined,
  BugOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DownOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { selectSelectedEntity } from '../../store/slices/sceneSlice';
import { interactionService } from '../../services/InteractionService';
import { InteractionType } from './InteractionEditor';
import './InteractionToolbar.less';

const { Text } = Typography;

interface InteractionToolbarProps {
  /** 是否显示调试信息 */
  showDebug?: boolean;
  /** 调试信息变化回调 */
  onDebugChange?: (show: boolean) => void;
  /** 是否启用预览模式 */
  previewMode?: boolean;
  /** 预览模式变化回调 */
  onPreviewModeChange?: (enabled: boolean) => void;
}

/**
 * 交互工具栏组件
 */
const InteractionToolbar: React.FC<InteractionToolbarProps> = ({
  showDebug = false,
  onDebugChange,
  previewMode = false,
  onPreviewModeChange
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const selectedEntity = useAppSelector(selectSelectedEntity);
  
  const [interactionDistance, setInteractionDistance] = useState(5);
  const [highlightEnabled, setHighlightEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);

  // 交互类型选项
  const interactionTypeOptions = [
    {
      key: InteractionType.CLICK,
      label: t('interaction.click'),
      icon: '👆'
    },
    {
      key: InteractionType.HOVER,
      label: t('interaction.hover'),
      icon: '👋'
    },
    {
      key: InteractionType.PROXIMITY,
      label: t('interaction.proximity'),
      icon: '🚶'
    },
    {
      key: InteractionType.GRAB,
      label: t('interaction.grab'),
      icon: '✋'
    },
    {
      key: InteractionType.TOUCH,
      label: t('interaction.touch'),
      icon: '👇'
    }
  ];

  /**
   * 创建交互组件
   */
  const handleCreateInteraction = useCallback((type: InteractionType) => {
    if (!selectedEntity) {
      console.warn('No entity selected');
      return;
    }

    const config = {
      interactionType: type,
      visible: true,
      interactive: true,
      interactionDistance,
      label: t('interaction.defaultLabel'),
      prompt: t('interaction.defaultPrompt'),
      highlightColor: '#ffff00',
      enableHighlight: highlightEnabled,
      enableSound: soundEnabled
    };

    // 使用交互服务创建组件
    interactionService.createInteractableComponent(selectedEntity, config);
  }, [selectedEntity, interactionDistance, highlightEnabled, soundEnabled, t]);

  /**
   * 删除交互组件
   */
  const handleDeleteInteraction = useCallback(() => {
    if (!selectedEntity) return;
    
    interactionService.removeInteractableComponent(selectedEntity);
  }, [selectedEntity]);

  /**
   * 复制交互组件
   */
  const handleCopyInteraction = useCallback(() => {
    if (!selectedEntity) return;
    
    // 这里应该实现复制逻辑
    console.log('Copy interaction component');
  }, [selectedEntity]);

  /**
   * 切换预览模式
   */
  const handleTogglePreview = useCallback(() => {
    const newMode = !previewMode;
    if (onPreviewModeChange) {
      onPreviewModeChange(newMode);
    }
  }, [previewMode, onPreviewModeChange]);

  /**
   * 切换调试模式
   */
  const handleToggleDebug = useCallback(() => {
    const newDebug = !showDebug;
    if (onDebugChange) {
      onDebugChange(newDebug);
    }
  }, [showDebug, onDebugChange]);

  /**
   * 渲染创建交互下拉菜单
   */
  const renderCreateMenu = () => {
    const items = interactionTypeOptions.map(option => ({
      key: option.key,
      label: (
        <Space>
          <span>{option.icon}</span>
          <span>{option.label}</span>
        </Space>
      ),
      onClick: () => handleCreateInteraction(option.key)
    }));

    return { items };
  };

  /**
   * 渲染设置弹出框
   */
  const renderSettingsPopover = () => (
    <div className="interaction-settings">
      <div className="setting-item">
        <Text strong>{t('interaction.interactionDistance')}</Text>
        <Slider
          min={0.5}
          max={20}
          step={0.5}
          value={interactionDistance}
          onChange={setInteractionDistance}
          tooltip={{ formatter: (value) => `${value}m` }}
        />
      </div>
      
      <Divider />
      
      <div className="setting-item">
        <Space>
          <Text>{t('interaction.enableHighlight')}</Text>
          <Switch
            checked={highlightEnabled}
            onChange={setHighlightEnabled}
            size="small"
          />
        </Space>
      </div>
      
      <div className="setting-item">
        <Space>
          <Text>{t('interaction.enableSound')}</Text>
          <Switch
            checked={soundEnabled}
            onChange={setSoundEnabled}
            size="small"
          />
        </Space>
      </div>
    </div>
  );

  // 检查当前实体是否有交互组件
  const hasInteraction = selectedEntity?.components?.Interactable;
  const interactionCount = interactionService.getStats().totalInteractions;

  return (
    <div className="interaction-toolbar">
      <Space split={<Divider type="vertical" />}>
        {/* 创建交互 */}
        <Dropdown menu={renderCreateMenu()} trigger={['click']}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            disabled={!selectedEntity}
          >
            {t('interaction.create')} <DownOutlined />
          </Button>
        </Dropdown>

        {/* 编辑/删除/复制 */}
        <Space>
          <Tooltip title={t('interaction.edit')}>
            <Button
              icon={<EditOutlined />}
              disabled={!hasInteraction}
            />
          </Tooltip>
          
          <Tooltip title={t('interaction.copy')}>
            <Button
              icon={<CopyOutlined />}
              disabled={!hasInteraction}
              onClick={handleCopyInteraction}
            />
          </Tooltip>
          
          <Tooltip title={t('interaction.delete')}>
            <Button
              icon={<DeleteOutlined />}
              disabled={!hasInteraction}
              onClick={handleDeleteInteraction}
              danger
            />
          </Tooltip>
        </Space>

        {/* 预览控制 */}
        <Space>
          <Tooltip title={previewMode ? t('interaction.stopPreview') : t('interaction.startPreview')}>
            <Button
              type={previewMode ? 'primary' : 'default'}
              icon={previewMode ? <StopOutlined /> : <PlayCircleOutlined />}
              onClick={handleTogglePreview}
            />
          </Tooltip>
          
          <Tooltip title={t('interaction.reload')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            />
          </Tooltip>
        </Space>

        {/* 可见性和调试 */}
        <Space>
          <Tooltip title={showDebug ? t('interaction.hideDebug') : t('interaction.showDebug')}>
            <Button
              type={showDebug ? 'primary' : 'default'}
              icon={showDebug ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={handleToggleDebug}
            />
          </Tooltip>
          
          <Badge count={interactionCount} size="small">
            <Tooltip title={t('interaction.debugInfo')}>
              <Button
                icon={<BugOutlined />}
                type={showDebug ? 'primary' : 'default'}
              />
            </Tooltip>
          </Badge>
        </Space>

        {/* 设置 */}
        <Popover
          content={renderSettingsPopover()}
          title={t('interaction.quickSettings')}
          trigger="click"
          placement="bottomRight"
        >
          <Tooltip title={t('interaction.settings')}>
            <Button icon={<SettingOutlined />} />
          </Tooltip>
        </Popover>
      </Space>

      {/* 状态指示器 */}
      <div className="toolbar-status">
        <Space>
          {selectedEntity && (
            <Text type="secondary">
              {t('interaction.selectedEntity')}: {selectedEntity.name || selectedEntity.id}
            </Text>
          )}
          
          {hasInteraction && (
            <Badge status="success" text={t('interaction.hasInteraction')} />
          )}
          
          {previewMode && (
            <Badge status="processing" text={t('interaction.previewActive')} />
          )}
        </Space>
      </div>
    </div>
  );
};

export default InteractionToolbar;
